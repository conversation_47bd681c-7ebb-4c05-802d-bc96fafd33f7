<!doctype html>
<html class="no-js" lang="">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title></title>
  <link rel="stylesheet" href="css/style.css">
  <meta name="description" content="">

  <meta property="og:title" content="">
  <meta property="og:type" content="">
  <meta property="og:url" content="">
  <meta property="og:image" content="">
  <meta property="og:image:alt" content="">

  <link rel="icon" href="/favicon.ico" sizes="any">
  <link rel="icon" href="/icon.svg" type="image/svg+xml">
  <link rel="apple-touch-icon" href="icon.png">

  <link rel="manifest" href="site.webmanifest">
  <meta name="theme-color" content="#fafafa">
</head>

<body>

  <!-- Add your site or application content here -->
  <iframe
    id="paytently-iframe"
    src=""
    width="100%"
    height="600"
    frameborder="0"
    title="Paytently Payment Form">
  </iframe>

  <div id="error-message" style="display: none; color: red; text-align: center; margin-top: 20px;">
    <p>Missing required parameters. Please ensure the URL contains both 'paymentId' and 'token' parameters.</p>
  </div>
  <script>
    // Function to get URL parameters
    function getUrlParameter(name) {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get(name);
    }

    // Get payment ID and token from URL parameters
    const paymentId = getUrlParameter('paymentId');
    const token = getUrlParameter('token');

    // Get iframe and error message elements
    const iframe = document.getElementById('paytently-iframe');
    const errorMessage = document.getElementById('error-message');

    // Check if both parameters are present
    if (paymentId && token) {
      // Construct the iframe URL with the parameters
      const iframeUrl = `https://sandbox.api.paytently.io/new-hosted-forms?paymentId=${encodeURIComponent(paymentId)}&token=${encodeURIComponent(token)}`;
      iframe.src = iframeUrl;
    } else {
      // Show error message if parameters are missing
      iframe.style.display = 'none';
      errorMessage.style.display = 'block';
    }
  </script>
  <script src="js/app.js"></script>

</body>

</html>
